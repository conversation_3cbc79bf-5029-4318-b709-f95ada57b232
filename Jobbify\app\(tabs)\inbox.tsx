import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAppContext, AppliedJob } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { useAuthCheck } from '@/utils/authCheck';
import { SmartLogo } from '@/components/SmartLogo';
import { CoverLetterStatusIndicator } from '@/components/CoverLetterStatusIndicator';
import { router } from 'expo-router';

const InboxScreen = () => {
  useAuthCheck();
  
  const { theme, applications, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  const insets = useSafeAreaInsets();
  
  const [activeInboxTab, setActiveInboxTab] = useState<'applications' | 'messages'>('applications');
  const [localApplications, setLocalApplications] = useState<AppliedJob[]>([]);
  const [coverLetterStatusMap, setCoverLetterStatusMap] = useState<Record<string, boolean>>({});
  
  // Initialize applications from context
  useEffect(() => {
    setLocalApplications(applications);
  }, [applications]);
  
  const openJobDetails = (job: any) => {
    // Navigate to job details or open modal
    console.log('Opening job details for:', job.title);
  };
  
  const openCoverLetterModal = (job: any) => {
    // Open cover letter modal
    console.log('Opening cover letter for:', job.title);
  };
  
  const renderApplicationItem = ({ item }: { item: AppliedJob }) => (
    <TouchableOpacity
      style={[styles.applicationItem, { backgroundColor: themeColors.card }]}
      onPress={() => openJobDetails(item.job)}
    >
      <SmartLogo
        companyName={item.job.company}
        size={50}
        style={styles.applicationLogo}
      />
      <View style={styles.applicationDetails}>
        <Text style={[styles.applicationJobTitle, { color: themeColors.text }]}>
          {item.job.title}
        </Text>
        <Text style={[styles.applicationCompany, { color: themeColors.textSecondary }]}>
          {item.job.company}
        </Text>
        <Text style={[styles.applicationDate, { color: themeColors.textSecondary }]}>
          Applied {new Date(item.appliedAt).toLocaleDateString()}
        </Text>
      </View>
      <View style={styles.applicationActions}>
        <CoverLetterStatusIndicator
          hasAttachment={coverLetterStatusMap[item.job.id] || false}
          attachmentType={coverLetterStatusMap[item.job.id] ? 'legacy' : 'none'}
          onPress={() => openCoverLetterModal(item.job)}
          themeColors={themeColors}
          size="small"
          showLabel={true}
        />
      </View>
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
      
      {/* Header */}
      <View style={[styles.header, { backgroundColor: themeColors.card, borderBottomColor: themeColors.border }]}>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>Inbox</Text>
      </View>
      
      {/* Inbox Tabs */}
      <View style={[styles.inboxTabSelector, { borderBottomColor: themeColors.border }]}>
        <TouchableOpacity
          style={[
            styles.inboxTabButton,
            activeInboxTab === 'applications' && [styles.activeInboxTabButton, { borderBottomColor: themeColors.tint }]
          ]}
          onPress={() => setActiveInboxTab('applications')}
        >
          <Text
            style={[
              styles.inboxTabText,
              { color: themeColors.textSecondary },
              activeInboxTab === 'applications' && { color: themeColors.text, fontWeight: 'bold' }
            ]}
          >
            Applications
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.inboxTabButton,
            activeInboxTab === 'messages' && [styles.activeInboxTabButton, { borderBottomColor: themeColors.tint }]
          ]}
          onPress={() => setActiveInboxTab('messages')}
        >
          <Text
            style={[
              styles.inboxTabText,
              { color: themeColors.textSecondary },
              activeInboxTab === 'messages' && { color: themeColors.text, fontWeight: 'bold' }
            ]}
          >
            Messages
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Content */}
      {activeInboxTab === 'applications' && (
        localApplications.length > 0 ? (
          <FlatList
            data={localApplications}
            keyExtractor={(item) => item.job.id}
            renderItem={renderApplicationItem}
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <View style={styles.emptyState}>
            <FontAwesome name="briefcase" size={60} color={themeColors.textSecondary} />
            <Text style={[styles.emptyStateTitle, { color: themeColors.text }]}>No Applications Yet</Text>
            <Text style={[styles.emptyStateSubtitle, { color: themeColors.textSecondary }]}>
              Start browsing and swipe right on jobs you're interested in to add them to your inbox.
            </Text>
          </View>
        )
      )}
      
      {activeInboxTab === 'messages' && (
        <View style={styles.emptyState}>
          <FontAwesome name="envelope" size={60} color={themeColors.textSecondary} />
          <Text style={[styles.emptyStateTitle, { color: themeColors.text }]}>No Messages Yet</Text>
          <Text style={[styles.emptyStateSubtitle, { color: themeColors.textSecondary }]}>
            Messages from employers will appear here.
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  inboxTabSelector: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    borderBottomWidth: 1,
  },
  inboxTabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
  },
  activeInboxTabButton: {
    borderBottomWidth: 2,
  },
  inboxTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContent: {
    paddingBottom: 20,
  },
  applicationItem: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 15,
    marginVertical: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  applicationLogo: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  applicationDetails: {
    flex: 1,
    marginLeft: 12,
  },
  applicationJobTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  applicationCompany: {
    fontSize: 14,
    marginBottom: 4,
  },
  applicationDate: {
    fontSize: 12,
  },
  applicationActions: {
    justifyContent: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default InboxScreen;