import React, { useState, useEffect, useRef } from 'react';
import { Layout<PERSON>nimation, UIManager, Platform, Animated } from 'react-native';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  SafeAreaView,
  KeyboardAvoidingView,
  Keyboard,
  Pressable,
  Image,
} from 'react-native';
import { useAppContext } from '@/context';
import { LightTheme, DarkTheme } from '@/constants';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { BlurView } from 'expo-blur';
import { BoldText, MediumText, RegularText } from '@/components/StyledText';

// Types and interfaces
type MessageType = 'user' | 'ai';

interface Message {
  id: string;
  text: string;
  type: MessageType;
  timestamp: Date;
}

interface AIFeature {
  id: string;
  title: string;
  description: string;
  route: string;
  icon: keyof typeof MaterialIcons.glyphMap;
}

const AI_TOOLS: AIFeature[] = [
  {
    id: 'resume',
    title: 'Resume Advisor',
    description: "Get feedback on your resume's strengths and weaknesses",
    route: '/(modals)/resume-advisor',
    icon: 'description',
  },
  {
    id: 'interview',
    title: 'Interview Coach',
    description: 'Practice interviews with AI feedback to improve your skills',
    route: '/(modals)/interview-coach',
    icon: 'psychology',
  },
  {
    id: 'skills',
    title: 'Skill Analyzer',
    description: 'Analyze your skills and get personalized recommendations',
    route: '/(modals)/skill-analyzer',
    icon: 'analytics',
  },
  {
    id: 'cover',
    title: 'Cover Letter Generator',
    description: 'Create personalized cover letters tailored to specific jobs',
    route: '/(modals)/cover-letter',
    icon: 'edit',
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerBar: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 10,
  },
  headerTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  chatContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  messagesArea: {
    flex: 1,
  },
  listContentContainer: {
    paddingHorizontal: 20,
    flexGrow: 1,
  },
  messageRow: {
    flexDirection: 'row',
    marginVertical: 8,
    paddingHorizontal: 4,
  },
  messageBubble: {
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderRadius: 24,
    maxWidth: '85%',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  userBubble: {
    borderBottomRightRadius: 8,
  },
  aiBubble: {
    borderBottomLeftRadius: 8,
  },
  toolsContainer: {
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  toolsHeader: {
    fontSize: 18,
    marginBottom: 16,
    fontWeight: '700',
    textAlign: 'center',
  },
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  toolCard: {
     width: '47%',
     minHeight: 140,
     alignItems: 'center',
     justifyContent: 'space-between',
     paddingVertical: 20,
     paddingHorizontal: 12,
     borderRadius: 20,
     overflow: 'visible',
     shadowColor: '#000',
     shadowOpacity: 0.1,
     shadowRadius: 10,
     shadowOffset: { width: 0, height: 4 },
     elevation: 5,
   },
   toolTextContainer: {
     alignItems: 'center',
     marginTop: 8,
     flex: 1,
     justifyContent: 'center',
   },
   toolTitle: {
     fontSize: 14,
     fontWeight: '700',
     marginBottom: 4,
     textAlign: 'center',
     lineHeight: 18,
   },
   toolDescription: {
     fontSize: 10,
     lineHeight: 13,
     textAlign: 'center',
     opacity: 0.8,
     paddingHorizontal: 2,
   },
   toolCardContainer: {
     marginBottom: 0,
   },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    flexShrink: 0,
  },
  inputArea: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 28,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
  },
  input: {
    flex: 1,
    minHeight: 44,
    maxHeight: 120,
    backgroundColor: 'transparent',
    paddingHorizontal: 4,
    paddingVertical: 8,
    fontSize: 16,
    marginRight: 12,
    lineHeight: 22,
  },
  sendButton: {
     width: 44,
     height: 44,
     borderRadius: 22,
     alignItems: 'center',
     justifyContent: 'center',
     shadowColor: '#000',
     shadowOpacity: 0.2,
     shadowRadius: 8,
     shadowOffset: { width: 0, height: 2 },
     elevation: 4,
   },
   aiAvatarContainer: {
     marginRight: 8,
     marginTop: 4,
   },
   aiAvatar: {
     width: 28,
     height: 28,
     borderRadius: 14,
     alignItems: 'center',
     justifyContent: 'center',
   },
   userAvatarContainer: {
     marginLeft: 8,
     marginTop: 4,
   },
   userAvatar: {
     width: 28,
     height: 28,
     borderRadius: 14,
     alignItems: 'center',
     justifyContent: 'center',
   },
   typingIndicator: {
     flexDirection: 'row',
     alignItems: 'center',
     paddingVertical: 8,
     paddingHorizontal: 4,
   },
   typingDot: {
     width: 6,
     height: 6,
     borderRadius: 3,
     marginHorizontal: 2,
     opacity: 0.6,
   },
 });

const AIScreen = () => {
  // Enable LayoutAnimation on Android
  useEffect(() => {
    if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }, []);

  const { theme } = useAppContext();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams<{ initialMessage?: string }>();

  const themeColors = theme === 'dark' ? DarkTheme : LightTheme;

  const [inputMessage, setInputMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const [showTools, setShowTools] = useState(true);
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const typingDot1 = useRef(new Animated.Value(0.6)).current;
  const typingDot2 = useRef(new Animated.Value(0.6)).current;
  const typingDot3 = useRef(new Animated.Value(0.6)).current;

  useEffect(() => {
    Animated.timing(logoScale, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  // Typing animation effect
  useEffect(() => {
    if (isTyping) {
      const animateTypingDots = () => {
        Animated.sequence([
          Animated.timing(typingDot1, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(typingDot2, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(typingDot3, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.parallel([
            Animated.timing(typingDot1, {
              toValue: 0.6,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(typingDot2, {
              toValue: 0.6,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(typingDot3, {
              toValue: 0.6,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
        ]).start(() => {
          if (isTyping) {
            animateTypingDots();
          }
        });
      };
      animateTypingDots();
    } else {
      // Reset dots to initial state
      typingDot1.setValue(0.6);
      typingDot2.setValue(0.6);
      typingDot3.setValue(0.6);
    }
  }, [isTyping]);

  useEffect(() => {
    if (params.initialMessage) {
      handleSend(params.initialMessage);
    } else {
      setMessages([
        {
          id: 'initial-ai-message',
          text: "Hello! I'm your AI career copilot. I can help with resumes, cover letters, interview prep, and more. What would you like help with today?", 
          type: 'ai',
          timestamp: new Date(),
        },
      ]);
    }
  }, [params.initialMessage]);

  useEffect(() => {
    if (messages.length > 1) {
      setShowTools(false);
    }
    if (messages.length > 0) {
      setTimeout(() => scrollToBottom(), 100);
    }
  }, [messages]);

  const handleSend = (text?: string) => {
    // Animate new message
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    const messageText = text || inputMessage.trim();
    if (!messageText) return;

    const userMessage: Message = {
      id: Date.now().toString() + '-user',
      text: messageText,
      type: 'user',
      timestamp: new Date(),
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    handleAIResponse(messageText);
    
    if(!text) {
        setInputMessage('');
    }
  };

  const handleAIResponse = (userMessageText: string) => {
    setIsTyping(true);
    setTimeout(() => {
      // Animate AI response
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      let responseText = "I'm having a little trouble understanding that. Could you rephrase?";
      const lowerCaseMessage = userMessageText.toLowerCase();

      if (lowerCaseMessage.includes('resume') || lowerCaseMessage.includes('cv')) {
        responseText = "I can help you improve your resume! Our Resume Advisor tool analyzes your resume and provides personalized feedback. Would you like to try it?";
      } else if (lowerCaseMessage.includes('cover letter')) {
        responseText = "Need help with a cover letter? I can generate tailored cover letters based on job descriptions with our Cover Letter Generator tool.";
      } else if (lowerCaseMessage.includes('interview')) {
        responseText = "Preparing for interviews is crucial. Our Interview Coach can help you practice with common questions and provide feedback on your answers.";
      } else if (lowerCaseMessage.includes('skill') || lowerCaseMessage.includes('gap')) {
        responseText = "I can analyze your skills to identify strengths and areas for improvement. Our Skill Analyzer tool provides personalized recommendations.";
      } else if (lowerCaseMessage.includes('hello') || lowerCaseMessage.includes('hi') || lowerCaseMessage.includes('hey')) {
        responseText = "Hi there! I'm your AI career assistant. I can help with resumes, cover letters, interview prep, and more. What would you like help with today?";
      } else if (lowerCaseMessage.includes('thank')) {
        responseText = "You're welcome! I'm here to help with your job search. Let me know if you need anything else.";
      } else {
        responseText = "I'm here to assist with your job search. I can help with resumes, cover letters, interview preparation, skill analysis, and job recommendations. What would you like to focus on today?";
      }

      const aiMessage: Message = {
        id: Date.now().toString() + '-ai',
        text: responseText,
        type: 'ai',
        timestamp: new Date(),
      };
      setMessages(prevMessages => [...prevMessages, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const scrollToBottom = (animated = true) => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated });
    }
  };

  const highlightKeywords = (text: string) => {
    if (!text) return text;
    const keywords = ['resume', 'cover letter', 'interview', 'skill'];
    const parts = text.split(new RegExp(`(${keywords.join('|')})`, 'gi'));
    return parts.map((part, idx) => {
      if (keywords.includes(part.toLowerCase())) {
        return (
          <MediumText key={idx} style={{ color: themeColors.tint }}>
            {part}
          </MediumText>
        );
      }
      return part;
    });
  };

  const renderMessage = ({ item }: { item: Message }) => {
    const isUser = item.type === 'user';
    return (
      <View style={[styles.messageRow, { justifyContent: isUser ? 'flex-end' : 'flex-start' }]}>
        {!isUser && (
          <View style={styles.aiAvatarContainer}>
            <View style={[styles.aiAvatar, { backgroundColor: themeColors.tint }]}>
              <MaterialIcons name="smart-toy" size={16} color="#fff" />
            </View>
          </View>
        )}
        <View
          style={[
            styles.messageBubble,
            isUser ? styles.userBubble : styles.aiBubble,
            {
              backgroundColor: isUser 
                ? themeColors.tint 
                : theme === 'dark' 
                  ? 'rgba(255, 255, 255, 0.1)' 
                  : 'rgba(255, 255, 255, 0.9)',
            },
          ]}
        >
          <RegularText style={{ 
            color: isUser ? '#fff' : themeColors.text,
            fontSize: 16,
            lineHeight: 22,
          }}>
            {isUser ? item.text : highlightKeywords(item.text)}
          </RegularText>
        </View>
        {isUser && (
          <View style={styles.userAvatarContainer}>
            <View style={[styles.userAvatar, { backgroundColor: themeColors.cardSecondary }]}>
              <MaterialIcons name="person" size={16} color={themeColors.text} />
            </View>
          </View>
        )}
      </View>
    );
  };

  const toolCardThemes = [
    {
      tool: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Purple gradient
      icon: '#FFFFFF',
      iconBg: 'rgba(102, 126, 234, 0.2)',
      shadow: '#667eea',
    },
    {
      tool: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', // Pink gradient
      icon: '#FFFFFF',
      iconBg: 'rgba(240, 147, 251, 0.2)',
      shadow: '#f093fb',
    },
    {
      tool: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // Blue gradient
      icon: '#FFFFFF',
      iconBg: 'rgba(79, 172, 254, 0.2)',
      shadow: '#4facfe',
    },
    {
      tool: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', // Green gradient
      icon: '#FFFFFF',
      iconBg: 'rgba(67, 233, 123, 0.2)',
      shadow: '#43e97b',
    },
  ];

  const renderToolCard = ({ item, index }: { item: AIFeature; index: number }) => {
    const { tool, icon, iconBg, shadow } = toolCardThemes[index % toolCardThemes.length];
    return (
      <Pressable
        onPress={() => router.push(item.route as any)}
        style={({ pressed }) => [
          {
            transform: [{ scale: pressed ? 0.95 : 1 }],
            opacity: pressed ? 0.9 : 1,
          },
          styles.toolCardContainer,
        ]}
      >
        <View
          style={[
            styles.toolCard,
            { 
              backgroundColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.9)',
              borderWidth: 1,
              borderColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
            },
          ]}
        >
          <View style={[styles.iconContainer, { backgroundColor: iconBg }]}>
             <MaterialIcons name={item.icon} size={24} color={shadow} />
           </View>
          <View style={styles.toolTextContainer}>
            <MediumText style={[styles.toolTitle, { color: themeColors.text }]}>{item.title}</MediumText>
            <RegularText style={[styles.toolDescription, { color: themeColors.textSecondary }]} numberOfLines={3}>
              {item.description}
            </RegularText>
          </View>
        </View>
      </Pressable>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar style={theme === 'dark' ? 'light' : 'dark'} translucent backgroundColor="transparent" />
      
      {/* Background overlay */}
      <View
        style={[
          StyleSheet.absoluteFill,
          {
            backgroundColor: theme === 'dark' 
              ? 'rgba(15, 15, 15, 0.95)'
              : 'rgba(248, 250, 252, 0.95)',
          },
        ]}
      />
      
      {/* Animated background logo */}
      <View
        style={[
          StyleSheet.absoluteFill,
          {
            alignItems: 'center',
            justifyContent: 'center',
            opacity: theme === 'dark' ? 0.05 : 0.08,
          },
        ]}
      >
        <Animated.Image
          source={require('@/assets/images/background-logo.png')}
          style={{
            width: '90%',
            height: '90%',
            transform: [{ scale: logoScale }],
          }}
          resizeMode="contain"
        />
      </View>

      {/* Modern minimal header */}
      <View style={[
        styles.headerBar, 
        { 
          paddingTop: insets.top + 8,
        }
      ]}>
        <View style={styles.headerTitle}>
          <MaterialIcons name="auto-awesome" size={20} color={themeColors.tint} style={{ marginRight: 8 }} />
          <BoldText style={{ color: themeColors.text, fontSize: 18 }}>AI Assistant</BoldText>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: 'rgba(255, 255, 255, 0.1)' }]}>
            <MaterialIcons name="history" size={18} color={themeColors.textSecondary} />
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: 'rgba(255, 255, 255, 0.1)' }]}>
            <MaterialIcons name="more-vert" size={18} color={themeColors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={0}
      >
        <View style={styles.chatContainer}>
          {/* Messages area */}
          <View style={styles.messagesArea}>
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessage}
              keyExtractor={item => item.id}
              contentContainerStyle={[
                styles.listContentContainer,
                { paddingTop: 20, paddingBottom: 20 }
              ]}
              showsVerticalScrollIndicator={false}
            />
            
            {/* Typing indicator */}
             {isTyping && (
               <View style={[styles.messageRow, { justifyContent: 'flex-start' }]}>
                 <View style={styles.aiAvatarContainer}>
                   <View style={[styles.aiAvatar, { backgroundColor: themeColors.tint }]}>
                     <MaterialIcons name="smart-toy" size={16} color="#fff" />
                   </View>
                 </View>
                 <View style={[styles.messageBubble,
                   styles.aiBubble,
                   {
                     backgroundColor: theme === 'dark' 
                       ? 'rgba(255, 255, 255, 0.1)' 
                       : 'rgba(255, 255, 255, 0.9)',
                   }
                 ]}>
                   <View style={styles.typingIndicator}>
                     <Animated.View style={[
                       styles.typingDot, 
                       { 
                         backgroundColor: themeColors.textSecondary,
                         opacity: typingDot1
                       }
                     ]} />
                     <Animated.View style={[
                       styles.typingDot, 
                       { 
                         backgroundColor: themeColors.textSecondary,
                         opacity: typingDot2
                       }
                     ]} />
                     <Animated.View style={[
                       styles.typingDot, 
                       { 
                         backgroundColor: themeColors.textSecondary,
                         opacity: typingDot3
                       }
                     ]} />
                   </View>
                 </View>
               </View>
             )}
             
             {/* AI Tools section */}
             {showTools && (
               <View style={styles.toolsContainer}>
                 <BoldText style={[styles.toolsHeader, { color: themeColors.text }]}>🚀 AI-Powered Tools</BoldText>
                 <View style={styles.toolsGrid}>
                   {AI_TOOLS.map((item, index) => (
                     <View key={item.id}>
                       {renderToolCard({ item, index })}
                     </View>
                   ))}
                 </View>
               </View>
             )}
          </View>

          {/* Input area */}
          <View style={[styles.inputArea, { paddingBottom: Math.max(insets.bottom + 20, 32) }]}>
            <BlurView intensity={100} tint={theme} style={styles.inputContainer}>
              <TextInput
                style={[styles.input, { color: themeColors.text }]}
                placeholder="Ask me anything..."
                placeholderTextColor={themeColors.textSecondary}
                value={inputMessage}
                onChangeText={setInputMessage}
                onSubmitEditing={() => handleSend()}
                returnKeyType="send"
                multiline
                maxLength={500}
              />
              <TouchableOpacity 
                onPress={() => handleSend()} 
                style={[
                  styles.sendButton, 
                  { 
                    backgroundColor: inputMessage.trim() ? themeColors.tint : themeColors.textSecondary,
                    transform: [{ scale: inputMessage.trim() ? 1 : 0.9 }]
                  }
                ]}
                disabled={!inputMessage.trim()}
              > 
                <Ionicons name="arrow-up" size={22} color="#fff" />
              </TouchableOpacity>
            </BlurView>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};


export default AIScreen;
