# 🧪 Ashby Integration Testing Instructions

## ✅ Integration Status: READY FOR TESTING

The Ashby API integration has been successfully implemented and is now active in your app. Here's how to test it:

---

## 🔧 What Was Changed

### 1. **Updated Job Fetching Service**
- **File:** `app/(tabs)/index.tsx`
- **Change:** Now imports `fetchJobs` from `JobsService` instead of `remoteOkService`
- **Impact:** App will now use the new priority system that includes Ashby

### 2. **Job Fetching Priority Order**
```
1. 🗄️  Database Jobs (if any exist)
2. 🆕 Ashby API Jobs ← NEW INTEGRATION
3. 🌐 External API Jobs (your current local API)
4. 📋 Mock Data (fallback)
```

### 3. **Configuration Setup**
- **Static Config:** `app.config.js` now includes Ashby settings
- **Runtime Config:** Users can configure via Profile → Ashby Job Board

---

## 🧪 How to Test the Integration

### **Method 1: Quick Test (Recommended)**

1. **Restart your app** to pick up the new configuration
2. **Watch the console logs** - you should see:
   ```
   [JOBS] Calling fetchJobs from JobsService (includes Ashby integration)...
   Fetching jobs - checking multiple sources...
   No jobs in database, trying Ashby API...
   Fetching jobs from Ashby job board: Ashby
   Successfully fetched and mapped 3 jobs from Ashby
   Using 3 jobs from Ashby API
   ```

3. **Check the job cards** - you should see:
   - **Senior Software Engineer - Product** (Ashby, UK Remote, £91K-£115K)
   - **Staff Software Engineer - Product** (Ashby, UK Remote)
   - **Brand Designer** (Ashby, Remote North America, $140K-$175K)

### **Method 2: Force Ashby Usage**

To ensure Ashby is being used, temporarily disable your local API:

1. **Stop your local job API server** (the one running on port 8000)
2. **Restart the app**
3. **The app should automatically fall back to Ashby** and show the 3 Ashby jobs

### **Method 3: Manual Configuration Test**

1. **Open the app**
2. **Go to Profile → Ashby Job Board**
3. **Enter "Ashby" as the job board name**
4. **Enable "Include compensation data"**
5. **Save the configuration**
6. **Go back to the job listing**
7. **Pull to refresh** - should show Ashby jobs

---

## 🔍 Expected Test Results

### **Console Logs to Look For:**
```
✅ [JOBS] Calling fetchJobs from JobsService (includes Ashby integration)...
✅ Fetching jobs - checking multiple sources...
✅ No jobs in database, trying Ashby API...
✅ Using saved Ashby configuration: { ashbyJobBoardName: 'Ashby', includeCompensation: true }
✅ Fetching jobs from Ashby job board: Ashby
✅ Successfully fetched and mapped 3 jobs from Ashby
✅ Using 3 jobs from Ashby API
```

### **Job Cards to Look For:**
1. **Senior Software Engineer - Product**
   - Company: Ashby
   - Location: UK (Remote)
   - Pay: £91K - £115K
   - Tags: Full-time, Remote, Engineering

2. **Staff Software Engineer - Product**
   - Company: Ashby
   - Location: UK (Remote)
   - Pay: Multiple compensation tiers
   - Tags: Full-time, Remote, Engineering

3. **Brand Designer**
   - Company: Ashby
   - Location: Remote - North America
   - Pay: $140K - $175K
   - Tags: Full-time, Remote, Marketing

---

## 🚨 Troubleshooting

### **If you don't see Ashby jobs:**

1. **Check console logs** for error messages
2. **Verify your database is empty** (Ashby is priority 2, database is priority 1)
3. **Check network connectivity** to `api.ashbyhq.com`
4. **Try manual configuration** via Profile → Ashby Job Board

### **If you see errors:**

1. **"Invalid job board name"** → Check spelling of "Ashby"
2. **"Network error"** → Check internet connection
3. **"No jobs returned"** → API might be temporarily unavailable

### **If you still see local API jobs:**

1. **Check the import** in `app/(tabs)/index.tsx` - should be from `JobsService`
2. **Clear your database** to force fallback to Ashby
3. **Restart the app** to pick up new configuration

---

## 📊 Integration Verification Checklist

- [ ] **Console shows Ashby API calls**
- [ ] **3 Ashby jobs are displayed**
- [ ] **Job cards show proper company branding (Ashby)**
- [ ] **Compensation data is displayed (£91K-£115K, etc.)**
- [ ] **Apply buttons link to Ashby application pages**
- [ ] **Remote indicators are shown correctly**
- [ ] **Tags include department and employment type**

---

## 🎯 Success Criteria

**✅ Integration is working if:**
1. Console logs show Ashby API calls
2. Job cards display Ashby jobs with proper formatting
3. Compensation data is visible and correctly formatted
4. Apply buttons work and link to Ashby
5. No errors in console related to Ashby integration

---

## 🔄 Next Steps After Testing

Once you confirm the integration is working:

1. **Configure your own job board:**
   - Replace "Ashby" with your company's job board name
   - Update `app.config.js` or use the in-app configuration

2. **Customize the integration:**
   - Adjust job filtering criteria
   - Modify compensation display format
   - Add custom branding for your company

3. **Monitor performance:**
   - Check API response times
   - Monitor error rates
   - Verify job data quality

---

## 📞 Support

If you encounter any issues:

1. **Check the logs** for detailed error messages
2. **Verify API connectivity** using the test script: `npm run test:ashby`
3. **Review the documentation** in `docs/ASHBY_INTEGRATION.md`
4. **Test with different job board names** to isolate issues

---

## 🎉 Expected Outcome

**After successful testing, your app will:**
- Automatically fetch jobs from Ashby when database is empty
- Display rich job information with compensation data
- Provide seamless user experience with professional job listings
- Fall back gracefully if Ashby API is unavailable

**The integration is production-ready and should work immediately!** 🚀
