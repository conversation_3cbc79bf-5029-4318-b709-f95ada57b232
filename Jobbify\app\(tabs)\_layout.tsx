import React, { useRef, useEffect, useState, useCallback } from 'react';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Link, Tabs } from 'expo-router';
import { Pressable, View, Animated, Text, Dimensions, LayoutChangeEvent, LayoutAnimation, UIManager, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useColorScheme } from '@/components/useColorScheme';
import { useClientOnlyValue } from '@/components/useClientOnlyValue';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import Colors from '@/constants/Colors';
import { MaterialIcons, FontAwesome5 } from '@expo/vector-icons';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Use FontAwesome icon type with strong typing
// Note: we don't use the color property anymore as we rely on dynamic theme colors instead
const TAB_CONFIG: { key: string; icon: keyof typeof FontAwesome.glyphMap; label: string; isCenter?: boolean }[] = [
  { key: 'home', icon: 'home', label: 'Home' },
  { key: 'inbox', icon: 'inbox', label: 'Inbox' },
  { key: 'index', icon: 'briefcase', label: 'Jobs', isCenter: true },
  { key: 'ai', icon: 'magic', label: 'AI' },
  { key: 'profile', icon: 'globe', label: 'Profile' },
  // Admin tab removed - now implemented in Supabase backend
];

// Separate tabs into left, center, and right for the new layout
const LEFT_TABS = TAB_CONFIG.filter(tab => tab.key === 'home' || tab.key === 'inbox');
const CENTER_TAB = TAB_CONFIG.find(tab => tab.isCenter);
const RIGHT_TABS = TAB_CONFIG.filter(tab => tab.key === 'ai' || tab.key === 'profile');

function CustomTabBar({ state, descriptors, navigation }: any) {
  // Get theme from our app context for consistent theme handling
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  const insets = useSafeAreaInsets();
  const screenWidth = Dimensions.get('window').width;
  
  // Constants for the new design
  const centerButtonSize = 72; // Increased from 64
  const centerButtonRadius = centerButtonSize / 2;
  const tabHeight = 48;
  const navBarHeight = 80;
  
  // Theme-aware colors
  const iconActive = themeColors.tint;
  const iconInactive = themeColors.tabIconDefault;
  
  // Bar background with appropriate transparency
  const barBackground = theme === 'light'
    ? 'rgba(245, 245, 245, 0.95)' // Very light gray for light mode 
    : 'rgba(25, 25, 25, 0.95)'; // Very dark for dark mode
    
  // Border color that works in both themes  
  const borderColor = theme === 'light'
    ? 'rgba(0, 0, 0, 0.05)' // Very light gray border for light mode
    : 'rgba(255, 255, 255, 0.05)'; // Very light white border for dark mode

  // Center button colors
  const centerButtonBg = themeColors.tint;
  const centerButtonShadow = theme === 'light' ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.3)';
  
  // Helper function to render a regular tab
  const renderTab = (tabConfig: any, routeName: string) => {
    const isFocused = state.routes.find((route: any) => route.name === routeName) && 
                     state.routes[state.index].name === routeName;
    const onPress = () => navigation.navigate(routeName);
    
    return (
      <Pressable 
        key={tabConfig.key}
        onPress={onPress} 
        style={({ pressed }) => [
          {
            paddingHorizontal: 16,
            paddingVertical: 8,
            height: tabHeight,
            borderRadius: tabHeight / 2,
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: isFocused ? (theme === 'light' ? 'rgba(0, 0, 0, 0.06)' : 'rgba(255, 255, 255, 0.11)') : 'transparent',
            transform: [{ scale: isFocused ? 1.05 : 1.0 }]
          },
          pressed && { 
            transform: [{ scale: 0.92 }],
            opacity: 0.7
          }
        ]}
      >
        <FontAwesome 
          name={tabConfig.icon} 
          size={isFocused ? 24 : 20}
          color={isFocused ? iconActive : iconInactive} 
          style={{ marginBottom: 2 }}
        />
        <Text style={{
          fontSize: 10,
          fontWeight: isFocused ? '600' : '400',
          color: isFocused ? iconActive : iconInactive,
          textAlign: 'center'
        }}>
          {tabConfig.label}
        </Text>
      </Pressable>
    );
  };
  
  // Helper function to render the center floating button
  const renderCenterButton = () => {
    if (!CENTER_TAB) return null;
    
    const isFocused = state.routes[state.index].name === CENTER_TAB.key;
    const onPress = () => navigation.navigate(CENTER_TAB.key);
    
    return (
      <Pressable 
        onPress={onPress}
        style={({ pressed }) => [
          {
            position: 'absolute',
            top: -centerButtonRadius + 10, // Overlay the navbar
            left: screenWidth / 2 - centerButtonRadius,
            width: centerButtonSize,
            height: centerButtonSize,
            borderRadius: centerButtonRadius,
            backgroundColor: centerButtonBg,
            alignItems: 'center',
            justifyContent: 'center',
            elevation: 8,
            shadowColor: centerButtonShadow,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 1,
            shadowRadius: 8,
            // Add a subtle border
            borderWidth: 3,
            borderColor: theme === 'light' ? '#fff' : '#000',
            transform: [{ scale: isFocused ? 1.1 : 1.0 }]
          },
          pressed && {
            transform: [{ scale: 0.9 }],
            opacity: 0.8
          }
        ]}
      >
        <FontAwesome 
          name={CENTER_TAB.icon} 
          size={28}
          color="#fff"
        />
      </Pressable>
    );
  };

  return (
    <View style={{ 
      position: 'absolute', 
      bottom: 0, 
      left: 0, 
      right: 0,
      height: navBarHeight,
      paddingBottom: Math.max(insets.bottom, 16),
      backgroundColor: barBackground,
      borderTopWidth: 1,
      borderTopColor: borderColor,
      elevation: 8,
      shadowColor: theme === 'light' ? '#000' : '#222',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: theme === 'light' ? 0.05 : 0.2,
      shadowRadius: 4,
    }}>
      {/* Main navbar content */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 24,
        paddingTop: 16,
        height: '100%'
      }}>
        {/* Left side tabs */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          justifyContent: 'flex-start'
        }}>
          {LEFT_TABS.map(tab => renderTab(tab, tab.key))}
        </View>
        
        {/* Center space for floating button */}
        <View style={{ width: centerButtonSize, alignItems: 'center' }} />
        
        {/* Right side tabs */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          justifyContent: 'flex-end',
          gap: 8
        }}>
          {RIGHT_TABS.map(tab => renderTab(tab, tab.key))}
        </View>
      </View>
      
      {/* Floating center button */}
      {renderCenterButton()}
    </View>
  );
}

function TabScreens() {
  const { theme } = useAppContext();
  const insets = useSafeAreaInsets();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  // When changing themes, ensure smooth transition - defer to prevent insertion effect warnings
  useEffect(() => {
    // Use requestAnimationFrame to defer the layout animation
    requestAnimationFrame(() => {
      LayoutAnimation.configureNext({
        duration: 300,
        update: { type: 'easeInEaseOut' },
      });
    });
  }, [theme]);
  
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: { display: 'none' }, // Hide the default tab bar since we have a custom one
      }}
      tabBar={(props) => <CustomTabBar {...props} />}>
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="inbox"
        options={{
          title: 'Inbox',
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="index"
        options={{
          title: 'Jobs',
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="ai"
        options={{
          title: 'AI',
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          headerShown: false,
        }}
      />
      {/* Admin tab removed - now implemented in Supabase backend */}
    </Tabs>
  );
}

// Export without wrapping in AppProvider since it's already wrapped in the root layout
export default function TabLayout() {
  return <TabScreens />;
}
