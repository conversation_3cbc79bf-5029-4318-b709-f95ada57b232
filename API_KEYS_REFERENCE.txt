# API KEYS REFERENCE
# This file contains all API keys and configuration needed for the Hireista/Jobbify application
# Keep this file secure and do not commit to version control

================================================================================
                                JOB API KEYS
================================================================================

1. ASHBY API
   - Purpose: Job listings from Ashby job boards
   - Key Variable: ASHBY_API_KEY
   - Documentation: https://developers.ashbyhq.com/
   - Usage: Used in ashbyJobsService.ts
   - Notes: Requires company-specific job board configuration

2. RAPIDAPI JOBS
   - Purpose: Job listings aggregation
   - Key Variable: RAPIDAPI_KEY
   - Documentation: https://rapidapi.com/
   - Usage: Used in rapidApiJobsService.ts
   - Notes: Multiple job sources available

3. REMOTEOK API
   - Purpose: Remote job listings
   - Key Variable: REMOTEOK_API_KEY (if required)
   - Documentation: https://remoteok.io/api
   - Usage: Used in remoteOkService.ts
   - Notes: Some endpoints may not require API key

================================================================================
                                AI API KEYS
================================================================================

1. OPENROUTER API
   - Purpose: AI model access for various LLMs
   - Key Variable: OPENROUTER_API_KEY
   - Documentation: https://openrouter.ai/docs
   - Usage: Used in openRouterService.ts
   - Models: GPT-4, Claude, Llama, etc.
   - Notes: Pay-per-use pricing

2. DEEPSEEK API
   - Purpose: AI assistance and code generation
   - Key Variable: DEEPSEEK_API_KEY
   - Documentation: https://platform.deepseek.com/
   - Usage: Used in deepseekService.ts
   - Notes: Alternative AI provider

3. OPENAI API (if used directly)
   - Purpose: GPT models for AI assistance
   - Key Variable: OPENAI_API_KEY
   - Documentation: https://platform.openai.com/docs
   - Usage: Direct OpenAI integration
   - Notes: May be used through OpenRouter instead

================================================================================
                            DATABASE & BACKEND
================================================================================

1. SUPABASE
   - Purpose: Database, authentication, and backend services
   - Key Variables:
     * SUPABASE_URL
     * SUPABASE_ANON_KEY
     * SUPABASE_SERVICE_ROLE_KEY (server-side only)
   - Documentation: https://supabase.com/docs
   - Usage: Used throughout the app for data storage
   - Notes: Main backend infrastructure

2. GOOGLE SERVICES (Android)
   - Purpose: Google authentication and services
   - File: google-services.json
   - Documentation: https://firebase.google.com/docs
   - Usage: Android authentication
   - Notes: Platform-specific configuration

================================================================================
                            FILE PROCESSING
================================================================================

1. FILE TEXT EXTRACTION API
   - Purpose: Extract text from PDF/document files
   - Key Variable: FILE_EXTRACTION_API_KEY
   - Usage: Used in fileTextExtractService.ts
   - Notes: For resume and document processing

================================================================================
                            ENVIRONMENT SETUP
================================================================================

For development, create a .env file in the Jobbify directory with:

# Job APIs
ASHBY_API_KEY=your_ashby_key_here
RAPIDAPI_KEY=your_rapidapi_key_here
REMOTEOK_API_KEY=your_remoteok_key_here

# AI APIs
OPENROUTER_API_KEY=your_openrouter_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
OPENAI_API_KEY=your_openai_key_here

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# File Processing
FILE_EXTRACTION_API_KEY=your_file_extraction_key

================================================================================
                            SECURITY NOTES
================================================================================

1. Never commit API keys to version control
2. Use environment variables for all sensitive data
3. Rotate keys regularly
4. Use different keys for development and production
5. Monitor API usage and costs
6. Keep this file secure and local only
7. Consider using a secrets management service for production

================================================================================
                            COST MONITORING
================================================================================

Monitor usage for:
- OpenRouter API calls (pay-per-use)
- Supabase database operations
- RapidAPI job fetching
- File processing API calls

Set up billing alerts where possible to avoid unexpected charges.

================================================================================
                            TROUBLESHOOTING
================================================================================

Common issues:
1. API key not working: Check expiration and permissions
2. Rate limiting: Implement proper retry logic
3. CORS issues: Ensure proper domain configuration
4. Authentication errors: Verify key format and headers

Last updated: [Current Date]
Maintained by: Development Team